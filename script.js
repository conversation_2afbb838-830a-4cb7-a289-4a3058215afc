$(document).ready(function () {
  // 平滑滚动导航
  $('.navbar-nav a[href^="#"]').on("click", function (e) {
    e.preventDefault();

    var target = this.hash;
    var $target = $(target);

    if ($target.length) {
      $("html, body").animate(
        {
          scrollTop: $target.offset().top - 80,
        },
        0,
        "swing"
      );

      // 在移动设备上关闭导航菜单
      if (window.innerWidth < 992) {
        $(".navbar-collapse").collapse("hide");
      }
    }
  });

  // 滚动时导航栏效果
  $(window).scroll(function () {
    var scrollTop = $(this).scrollTop();

    // 导航栏背景变化
    if (scrollTop > 50) {
      $(".navbar").addClass("scrolled");
    } else {
      $(".navbar").removeClass("scrolled");
    }

    // 滚动到顶部按钮显示/隐藏
    if (scrollTop > 300) {
      $(".scroll-top").addClass("visible");
    } else {
      $(".scroll-top").removeClass("visible");
    }

    // 移除了自动高亮检测功能

    // 滚动动画
    animateOnScroll();
  });

  // 移除了自动高亮检测功能，现在只使用hover效果

  // 滚动动画
  function animateOnScroll() {
    $(".fade-in-up").each(function () {
      var elementTop = $(this).offset().top;
      var elementBottom = elementTop + $(this).outerHeight();
      var viewportTop = $(window).scrollTop();
      var viewportBottom = viewportTop + $(window).height();

      if (elementBottom > viewportTop && elementTop < viewportBottom) {
        $(this).addClass("visible");
      }
    });
  }

  // 初始化动画元素
  function initAnimations() {
    // 为需要动画的元素添加fade-in-up类
    $(
      ".hero-content, .section-title, .section-description, .feature-card"
    ).addClass("fade-in-up");

    // 延迟显示英雄内容
    setTimeout(function () {
      $(".hero-content").addClass("visible");
    }, 300);
  }

  // 滚动到顶部功能
  function addScrollToTop() {
    $("body").append(
      '<button class="scroll-top"><i class="fas fa-arrow-up"></i></button>'
    );

    $(".scroll-top").on("click", function () {
      $("html, body").animate(
        {
          scrollTop: 0,
        },
        0,
        "swing"
      );
    });
  }

  // 按钮悬停效果
  function initButtonEffects() {
    $(".btn").hover(
      function () {
        $(this).addClass("btn-hover");
      },
      function () {
        $(this).removeClass("btn-hover");
      }
    );
  }

  // 特色功能卡片悬停效果
  function initFeatureCardEffects() {
    $(".feature-card").hover(
      function () {
        $(this).find(".feature-icon img").addClass("animate-pulse");
      },
      function () {
        $(this).find(".feature-icon img").removeClass("animate-pulse");
      }
    );
  }

  // 导航栏响应式处理
  function handleNavbarResponsive() {
    $(window).resize(function () {
      if (window.innerWidth >= 992) {
        $(".navbar-collapse").removeClass("show");
      }
    });
  }

  // 预加载图片
  function preloadImages() {
    var images = [
      "https://ccdn1.goodq.top/caches/a4e466277a25c095c62c095e6259acb4/aHR0cHM6Ly81ZDIzZTllNjYyNGUwLnQ3My5xaWZlaXllLmNvbS9xZnktY29udGVudC91cGxvYWRzLzIwMTkvMDcvZTdmY2MzMDYzNThlNzI2YmQ5OTcxZGZlOTUxODBmZjgtOTAud2VicA_p_p100_p_3D_p_p100_p_3D.webp",
      "https://ccdn1.goodq.top/caches/a4e466277a25c095c62c095e6259acb4/aHR0cHM6Ly81ZDIzZTllNjYyNGUwLnQ3My5xaWZlaXllLmNvbS9xZnktY29udGVudC91cGxvYWRzLzIwMTkvMDcvNjljZDY2MWNjZjM4ODgxYzdmMTNkMWU2Nzc1ODJkNTctOTAud2VicA_p_p100_p_3D_p_p100_p_3D.webp",
    ];

    images.forEach(function (src) {
      var img = new Image();
      img.src = src;
    });
  }

  // 添加CSS动画类
  function addAnimationClasses() {
    var style = document.createElement("style");
    style.textContent = `
            .animate-pulse {
                animation: pulse 1s ease-in-out infinite;
            }
            
            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.1); }
            }
            
            .btn-hover {
                transform: translateY(-2px) !important;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
            }
            
            .navbar.scrolled {
                background: rgba(255, 255, 255, 0.98) !important;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
            }
        `;
    document.head.appendChild(style);
  }

  // 初始化所有功能
  function init() {
    initAnimations();
    addScrollToTop();
    initButtonEffects();
    initFeatureCardEffects();
    handleNavbarResponsive();
    preloadImages();
    addAnimationClasses();

    // 移除了初始检查滚动位置的功能，只保留动画
    animateOnScroll();
  }

  // 页面加载完成后初始化
  init();

  // 窗口加载完成后的额外处理
  $(window).on("load", function () {
    // 确保所有图片加载完成后再执行动画
    setTimeout(function () {
      $(".hero-image img").addClass("animate-float");
      $(".feature-image img").addClass("animate-shake");
    }, 500);
  });

  // 错误处理
  window.addEventListener("error", function (e) {
    console.log("发生错误:", e.error);
  });

  // 调试信息（开发环境）
  if (
    window.location.hostname === "localhost" ||
    window.location.hostname === "127.0.0.1"
  ) {
    console.log("jQuery版本:", $.fn.jquery);
    console.log("Bootstrap已加载");
    console.log("自定义脚本已初始化");
  }
});

// 全局函数：平滑滚动到指定元素
function scrollToElement(elementId) {
  var $target = $(elementId);
  if ($target.length) {
    $("html, body").animate(
      {
        scrollTop: $target.offset().top - 80,
      },
      0,
      "swing"
    );
  }
}

// 全局函数：显示/隐藏加载动画
function toggleLoading(show) {
  if (show) {
    $("body").append(
      '<div class="loading-overlay"><div class="spinner"></div></div>'
    );
  } else {
    $(".loading-overlay").remove();
  }
}
