/* 自定义字体 */
@import url("https://fonts.googleapis.com/css2?family=Source+Han+Sans+CN:wght@400;500;700&display=swap");

/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
  line-height: 1.6;
  color: #596e80;
}

/* 导航栏样式 */
.header-section {
  position: relative;
  z-index: 1000;
}

.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  padding: 8px 0;
}

/* Logo文字样式 */
.logo-main {
  font-size: 28px;
  font-weight: 700;
  color: #4185f4;
  letter-spacing: 2px;
  font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
  text-decoration: none;
}

.navbar-brand {
  transition: transform 0.3s ease;
}

.navbar-brand:hover {
  transform: scale(1.05);
  text-decoration: none;
}

.navbar-brand:hover .logo-main {
  color: #0075f8;
}

.navbar-nav .nav-link {
  color: #222d39 !important;
  font-weight: 500;
  margin: 0 8px;
  padding: 6px 16px !important;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
  background-color: #4185f4;
  color: #ffffff !important;
  transform: translateY(-2px);
}

/* 英雄区域样式 */
.hero-section {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #e8f3fc 0%, #edf5fc 50%, #e8f3fc 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-shape {
  position: absolute;
  width: 48%;
  height: 100%;
  opacity: 0.5;
  transform: translate(-52%, 0px) rotate(20deg);
  fill: #e8f3fc;
  animation: fillAnimation 8s infinite;
}

@keyframes fillAnimation {
  0% {
    fill: #e8f3fc;
  }
  50% {
    fill: #edf5fc;
  }
  100% {
    fill: #e8f3fc;
  }
}

.hero-content {
  position: relative;
  z-index: 2;
  padding-top: 3vh;
}

.hero-title {
  font-size: 50px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 30px;
}

.hero-title .text-muted {
  color: #666666 !important;
}

.hero-title .text-primary {
  color: #222d39 !important;
  font-size: 60px;
}

.hero-description {
  font-size: 14px;
  color: #677294;
  line-height: 30px;
  margin-bottom: 45px;
  max-width: 500px;
}

.hero-buttons {
  margin-top: 17px;
}

.hero-buttons .btn {
  padding: 17px 30px;
  font-size: 13px;
  letter-spacing: 2px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.hero-buttons .btn-primary {
  background-color: #4185f4;
  border-color: #4185f4;
}

.hero-buttons .btn-primary:hover {
  background-color: #0075f8;
  border-color: #0075f8;
  transform: translateY(-2px);
}

.hero-buttons .btn-outline-dark {
  background-color: #222d39;
  border-color: #222d39;
  color: #ffffff;
}

.hero-buttons .btn-outline-dark:hover {
  background-color: #677294;
  border-color: #677294;
  color: #ffffff;
  transform: translateY(-2px);
}

.hero-image {
  position: relative;
  z-index: 2;
  text-align: center;
}

.hero-image img {
  max-width: 100%;
  height: auto;
}

/* 动画效果 */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-shake {
  animation: shake 15s ease-in-out infinite;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

/* 特色功能区域样式 */
.features-section {
  background-color: #ffffff;
  padding: 13vh 0 13vh 0;
}

.section-title {
  font-size: 40px;
  font-weight: 500;
  letter-spacing: 2px;
  margin-bottom: 10px;
}

.section-title .text-dark {
  color: #222d39 !important;
}

.section-title .text-primary {
  color: #4185f4 !important;
}

.section-divider {
  font-size: 26px;
  line-height: 10px;
  letter-spacing: 2px;
  margin-bottom: 20px;
}

.section-description {
  font-size: 14px;
  color: #677294;
  line-height: 26px;
  max-width: 600px;
  margin: 0 auto;
}

.feature-image {
  text-align: center;
  padding-right: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
}

.feature-image img {
  max-width: 100%;
  height: auto;
}

.feature-cards-container {
  padding-left: 0;
}

.feature-cards-container .row {
  margin-bottom: 30px;
}

.feature-cards-container .row:last-child {
  margin-bottom: 0;
}

.feature-card {
  display: flex;
  align-items: flex-start;
  padding: 28px 20px 30px 20px;
  margin: 15px;
  border: 1px solid #ececec;
  border-radius: 4px;
  transition: all 0.3s ease;
  background-color: #ffffff;
  height: 100%;
}

.feature-card:hover {
  border-color: #ffffff;
  box-shadow: 0px 0px 15px #deebf2;
  transform: translateY(-5px);
}

.feature-icon {
  margin-right: 18px;
  padding: 2px;
  flex-shrink: 0;
}

.feature-icon img {
  width: 43px;
  height: 43px;
}

.feature-content {
  flex: 1;
}

.feature-content h4 {
  font-size: 18px;
  font-weight: 700;
  color: #222d39;
  margin-bottom: 8px;
}

.feature-content p {
  font-size: 13px;
  color: #677294;
  line-height: 26px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  /* Logo响应式 */
  .logo-main {
    font-size: 24px;
    letter-spacing: 1px;
  }

  .hero-title {
    font-size: 28px;
  }

  .hero-title .text-primary {
    font-size: 30px;
  }

  .hero-description {
    font-size: 16px;
  }

  .section-title {
    font-size: 28px;
  }

  .feature-image {
    padding-right: 0;
    margin-bottom: 30px;
  }

  .feature-card {
    margin: 5px 0;
    padding: 10px;
  }

  .feature-content h4 {
    font-size: 16px;
  }

  .feature-content p {
    font-size: 12px;
  }
}

/* 淡入动画 */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in-up.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 产品介绍区域样式 */
.about-section {
  background: linear-gradient(135deg, #e8f3fc 0%, #edf5fc 50%, #e8f3fc 100%);
  padding: 13vh 0;
}

.product-card {
  background-color: #ffffff;
  border: none;
  border-bottom: 3px solid #4185f4;
  border-radius: 0;
  padding: 30px 40px 40px;
  margin-top: 40px;
  transition: all 0.3s ease;
  height: 100%;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.product-icon {
  margin-bottom: 15px;
}

.icon-wrapper {
  position: relative;
  display: inline-block;
  width: 65px;
  height: 65px;
}

.icon-wrapper img {
  position: absolute;
  top: 0;
  left: 0;
  width: 65px;
  height: 65px;
  transition: opacity 0.2s linear;
}

.icon-wrapper .front-image {
  opacity: 1;
  z-index: 2;
}

.icon-wrapper .back-image {
  opacity: 0;
  z-index: 1;
}

.product-card:hover .icon-wrapper .front-image {
  opacity: 0;
}

.product-card:hover .icon-wrapper .back-image {
  opacity: 1;
}

.product-card h5 {
  font-size: 18px;
  font-weight: 700;
  color: #222d39;
  margin: 20px 0 15px;
}

.product-card p {
  font-size: 13px;
  color: #677294;
  line-height: 26px;
  margin: 0;
  max-width: 300px;
  margin: 0 auto;
}

/* 工作流程区域样式 */
.workflow-section {
  background-color: #ffffff;
  padding: 13vh 0;
}

.workflow-step {
  padding: 30px 20px;
  background-color: #ffffff;
  border: none;
  transition: all 0.3s ease;
  height: 100%;
}

.workflow-step:hover {
  transform: translateY(-5px);
}

.workflow-icon {
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.workflow-icon img {
  width: 90px;
  height: 90px;
  transition: transform 0.3s ease;
}

.workflow-step:hover .workflow-icon img {
  transform: scale(1.1);
}

.workflow-step h5 {
  font-size: 18px;
  font-weight: 700;
  color: #222d39;
  margin: 20px 0 10px;
}

.workflow-desc {
  font-size: 14px;
  color: #677294;
  line-height: 1.6;
  margin: 0;
}

/* 统计数据区域样式 */
.statistics-section {
  background: linear-gradient(
      135deg,
      rgba(34, 45, 57, 0.7),
      rgba(34, 45, 57, 0.7)
    ),
    url("images/stats-bg.webp");
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
  padding: 14vh 0;
  color: #ffffff;
}

.stat-item {
  padding: 20px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-icon {
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon img {
  width: 90px;
  height: 90px;
  transition: transform 0.3s ease;
}

.stat-item:hover .stat-icon img {
  transform: scale(1.1);
}

.stat-number {
  font-family: "Oswald", sans-serif;
  font-size: 46px;
  font-weight: normal;
  color: #ffffff;
  margin-bottom: 5px;
  line-height: 1;
}

.stat-label {
  font-family: "微软雅黑", sans-serif;
  font-size: 18px;
  font-weight: normal;
  color: #ffffff;
  margin: 0;
}

/* 下载平台区域样式 */
.download-section {
  background: linear-gradient(135deg, #e8f3fc 0%, #edf5fc 50%, #e8f3fc 100%);
  padding: 13vh 0;
}

.download-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
}

.download-image img {
  max-width: 240px;
  height: auto;
  transition: transform 0.3s ease;
}

.download-image:hover img {
  transform: scale(1.05);
}

.download-content {
  padding-left: 40px;
  text-align: center;
}

.download-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 15px;
  letter-spacing: 2px;
}

.download-divider {
  margin-bottom: 20px;
  font-size: 26px;
  line-height: 10px;
  letter-spacing: 2px;
}

.download-description {
  font-size: 14px;
  color: #677294;
  line-height: 26px;
  margin-bottom: 30px;
  font-family: "微软雅黑", sans-serif;
}

.download-buttons {
  display: flex;
  gap: 50px;
  flex-wrap: wrap;
  justify-content: center;
}

.download-btn {
  display: inline-block;
  min-width: 140px;
  padding: 35px 20px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 2px;
  border: 1px solid #4185f4;
  border-radius: 8px;
  background-color: #4185f4;
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  text-align: center;
}

.download-btn:hover {
  background-color: #0075f8;
  border-color: #0075f8;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.download-btn i {
  font-size: 18px;
  margin-right: 8px;
  margin-top: 1px;
}

@media (max-width: 768px) {
  .download-content {
    padding-left: 0;
    text-align: center;
    margin-top: 30px;
  }

  .download-buttons {
    justify-content: center;
  }

  .download-btn {
    flex: 1;
    min-width: 120px;
  }
}

/* 常见问题区域样式 */
.faq-section {
  background-color: #ffffff;
  padding: 13vh 0;
}

.accordion-item {
  border: 1px solid #e9ecef;
  border-radius: 8px !important;
  margin-bottom: 15px;
  overflow: hidden;
}

.accordion-button {
  background-color: #deeefc;
  color: #222d39;
  font-weight: 600;
  font-size: 16px;
  padding: 20px 25px;
  border: none;
  border-radius: 8px !important;
}

.accordion-button:not(.collapsed) {
  background-color: #4185f4;
  color: #ffffff;
  box-shadow: none;
}

.accordion-button:focus {
  box-shadow: none;
  border-color: transparent;
}

.accordion-body {
  padding: 20px 25px;
  font-size: 14px;
  color: #677294;
  line-height: 28px;
  background-color: #ffffff;
}

.faq-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.faq-image img {
  max-width: 100%;
  height: auto;
}

/* Contact Section */
.contact-section {
  padding: 5rem 0;
  background-color: #24262b;
  color: white;
}

.contact-section .section-subtitle {
  color: #4185f4;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 0.5rem;
}

.contact-section .section-title {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.contact-section .section-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin-bottom: 0;
}

.contact-info h4 {
  color: white;
  margin-bottom: 2rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
}

.contact-item i {
  color: #4185f4;
  font-size: 1.2rem;
  margin-right: 1rem;
  width: 20px;
}

/* 二维码容器样式 */
.qr-code-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.qr-codes .qr-code-small {
  max-width: 80px;
  height: auto;
  transition: transform 0.3s ease;
}

.qr-code-container:hover .qr-code-small {
  transform: scale(1.1);
}

/* 大图悬浮层 */
.qr-code-large {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  pointer-events: none;
}

.qr-code-large img {
  width: 200px;
  height: 200px;
  border-radius: 5px;
}

.qr-code-container:hover .qr-code-large {
  opacity: 1;
  visibility: visible;
  transform: translate(-50%, -50%) scale(1);
}

/* 添加一个小箭头指示 */
.qr-code-large::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid white;
}

.qr-codes p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-bottom: 0;
}

.social-icons {
  margin-bottom: 2rem;
}

.social-icon {
  display: inline-block;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  text-align: center;
  line-height: 40px;
  margin: 0 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-icon:hover {
  background-color: #4185f4;
  color: white;
  transform: translateY(-3px);
}

.footer-links p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.copyright {
  font-size: 0.8rem !important;
  color: rgba(255, 255, 255, 0.6) !important;
}

/* 滚动到顶部按钮 */
.scroll-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background-color: #4185f4;
  color: #ffffff;
  border: none;
  border-radius: 50%;
  font-size: 20px;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.scroll-top.visible {
  opacity: 1;
  visibility: visible;
}

.scroll-top:hover {
  background-color: #0075f8;
  transform: translateY(-3px);
}

/* 页脚样式 */
.footer-section {
  background-color: #222d39;
  color: #ffffff;
  text-align: center;
}

.footer-section p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}
